"use client";
import React, { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import UpdateEmployee from "../manage_employee/UpdateEmployee";
import DeleteRow from "@/app/_component/DeleteRow";
import { client_routes } from "@/lib/routePath";
import UpdateClient from "./UpdateClient";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import ClientCarrierSetup from "./manage_client_carrier/clientcarriersetup";
import { useRouter, useSearchParams } from "next/navigation";
import { Branch } from "../manage_branch/Column";
import { Employee } from "../manage_employee/column";
import { ColDef } from "ag-grid-community";
import { min } from "date-fns";
import ClientDetailsPopup from "./ClientDetailsPopup";
import PinnedHeader from "@/app/_component/PinnedHeader";

export interface Client {
  branch: any;
  branch_id: any;
  user: any;
  id: any;
  client_name: string;
  ownership: string;
  country: string;
  client_id: any;
  corporation_id: any;
  created_at: any;
  updated_at: any;
  associateId: any;
  associate: any;
  // permissions: string[];
}

// Define this component above or outside your column definition
const TrackSheetWithPopup = ({
  client,
  permissions,
  userData,
  clientId,
}: any) => {
  console.log("Client ID in TrackSheetWithPopup:", clientId);
  const [isOpen, setIsOpen] = useState(false);
  const [showAddCustomField, setShowAddCustomField] = useState(false);
  //console.log(userData)

  function openAddCustomField() {
  setShowAddCustomField(true);
}

  return (
    <div className="flex items-center">
      <PermissionWrapper
        permissions={permissions}
        requiredPermissions={[
          "view-setup",
          "update-setup",
          "create-setup",
          "delete-setup",
          "view-client",
          "update-client",
          "delete-client",
          "create-client",
        ]}
      >
        <Button
          variant="customButton"
          className="cursor-pointer capitalize bg-blue-500 text-white hover:bg-blue-600 h-4 w-full"
          onClick={() => setIsOpen(true)}
          title={`Track Sheet for ${client?.client_name}`}
        >
          Add / Update
        </Button>
        {/* Pass open state to ClientDetailsPopup */}
        <ClientDetailsPopup
          open={isOpen}
          setOpen={setIsOpen}
          userData={userData}
          clientId={clientId}
          clientName={client?.client_name}
          onTriggerAddCustomField={() => setShowAddCustomField(true)}
        />
      </PermissionWrapper>
    </div>
  );
};

export interface Carrier {
  name: string;
  register1: string;
  code: string;
  country: string;
  state: string;
  city: string;
  address: string;
  phone: string;
  postalcode: string;
  carrier_id: any;
}

export const column = (
  permissions: string[],
  allBranch: any[],
  allUser: any[],
  allAssociate: any[],
  userData: any[],
  router: ReturnType<typeof useRouter>
) => {
  const columns = [
    {
      field: "client_name",
      headerName: "Client Name",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },

      headerComponent: PinnedHeader,
      width: 300,
    },
    {
      field: "associate",
      headerName: "Associate",
      valueGetter: (params) => params.data?.associate?.name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },

      headerComponent: PinnedHeader,
    },
    {
      field: "user",
      headerName: "Ownership",
      valueGetter: (params) => params.data?.ownership?.username || "",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },

      headerComponent: PinnedHeader,
    },
    {
      field: "branch",
      headerName: "Branch",
      valueGetter: (params) => params.data?.branch?.branch_name,
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },

      headerComponent: PinnedHeader,
    },
    {
      field: "payment terms",
      headerName: "Payment Terms",
      cellRenderer: (params: any) => {
        const client = params?.data;
        return (
          <div className="flex items-center">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-setup"]}
            >
              <Button
                variant="customButton"
                className="cursor-pointer capitalize bg-gray-500 text-white hover:bg-gray-600 h-4 w-full"
                onClick={() => {
                  console.time("PaymentTermsLoadTime"); // Start timer
                  router.push(
                    `/pms/manage_client/manage_client_carrier/${client?.id}`
                  );
                  console.timeEnd("PaymentTermsLoadTime"); // End timer and log time
                }}
                title={client?.client_name}
              >
                Add
              </Button>
            </PermissionWrapper>
          </div>
        );
      },
      sortable: false,
      width: 130,
      minWidth: 100,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center", // Fallback
      },
    },
    {
      field: "track_sheet",
      headerName: "Custom Fields",
      cellRenderer: (params: any) => {
        const client = params?.data;
        console.log("Client ID in cellRenderer:", client?.id);
        return (
          <TrackSheetWithPopup
            client={client}
            permissions={permissions}
            userData={userData}
            clientId={client?.id}
          />
        );
      },
      sortable: false,
      width: 130,
      minWidth: 100,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center",
      },
    },
    {
      field: "action",
      headerName: "Action",
      cellRenderer: (params: any) => {
        const client = params?.data;
        return (
          <div className="flex items-center">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["update-client"]}
            >
              <UpdateClient
                data={client}
                allBranch={allBranch}
                allUser={allUser}
                allAssociate={allAssociate}
              />
            </PermissionWrapper>

            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["delete-client"]}
            >
              <DeleteRow
                route={`${client_routes.DELETE_CLIENT}/${client?.id}`}
              />
            </PermissionWrapper>
          </div>
        );
      },
      sortable: false,
      width: 130,
      minWidth: 100,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center", // Fallback
      },
    },
  ];

  return columns;
};
