"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/invoiceFile/create");
const view_1 = require("../../controllers/invoiceFile/view");
const viewAll_1 = require("../../controllers/invoiceFile/viewAll");
const viewByUser_1 = require("../../controllers/invoiceFile/viewByUser");
const update_1 = require("../../controllers/invoiceFile/update");
const delete_1 = require("../../controllers/invoiceFile/delete");
const authentication_1 = require("../../../middleware/authentication");
const router = (0, express_1.Router)();
// Create new invoice file entry
router.post("/", authentication_1.authenticate, create_1.createInvoiceFile);
// Get single invoice file by ID
router.get("/:id", 
// authenticate,
view_1.viewInvoiceFile);
// Get all invoice files with pagination
router.get("/", 
// authenticate,
viewAll_1.viewAllInvoiceFiles);
// Get invoice files assigned to specific user
router.get("/user/:userId", 
// authenticate,
viewByUser_1.viewInvoiceFilesByUser);
// Update invoice file by ID
router.put("/:id", 
// authenticate,
update_1.updateInvoiceFile);
// Soft delete invoice file by ID
router.delete("/:id", 
// authenticate,
delete_1.deleteInvoiceFile);
exports.default = router;
//# sourceMappingURL=invoiceFileRoutes.js.map