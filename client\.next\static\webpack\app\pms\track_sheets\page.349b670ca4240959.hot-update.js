"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/track_sheets/page",{

/***/ "(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx":
/*!***************************************************!*\
  !*** ./app/pms/track_sheets/ClientSelectPage.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/_component/FancySelect */ \"(app-pages-browser)/./app/_component/FancySelect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ExportTrackSheet__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ExportTrackSheet */ \"(app-pages-browser)/./app/pms/track_sheets/ExportTrackSheet.tsx\");\n/* harmony import */ var _ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ViewTrackSheet */ \"(app-pages-browser)/./app/pms/track_sheets/ViewTrackSheet.tsx\");\n/* harmony import */ var _app_user_trackSheets_TrackSheetContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/user/trackSheets/TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ClientSelectPage = (param)=>{\n    let { permissions, client, carrierDataUpdate, clientDataUpdate, userData } = param;\n    _s();\n    const [customFieldsMap, setCustomFieldsMap] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedClients, setSelectedClients] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trackSheetData, setTrackSheetData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        data: [],\n        datalength: 0\n    });\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const pageSize = parseInt(searchParams.get(\"pageSize\")) || 50;\n    const totalPages = Math.ceil(((trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.datalength) || 0) / pageSize);\n    const params = new URLSearchParams(searchParams);\n    const clientOptions = (client === null || client === void 0 ? void 0 : client.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.client_name\n        };\n    })) || [];\n    const mapCustomFields = (data)=>data.map((row)=>({\n                ...row,\n                customFields: (row.TrackSheetCustomFieldMapping || []).reduce((acc, mapping)=>{\n                    acc[mapping.customFieldId] = mapping.value;\n                    return acc;\n                }, {})\n            }));\n    const handleClientChange = (newSelectedClients)=>{\n        setSelectedClients(newSelectedClients);\n    };\n    // const  {customFieldsReloadTrigger} = useContext()\n    const { customFieldsReloadTrigger, deleteData, warningFilter } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_app_user_trackSheets_TrackSheetContext__WEBPACK_IMPORTED_MODULE_8__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            if (selectedClients.length > 0) {\n                try {\n                    var _selectedClients_, _selectedClients_1;\n                    const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value));\n                    const fieldsMap = {};\n                    (response.custom_fields || []).forEach((field)=>{\n                        fieldsMap[field.id] = {\n                            name: field.name,\n                            type: field.type\n                        };\n                    });\n                    setCustomFieldsMap(fieldsMap);\n                    if (!params.get(\"page\")) params.set(\"page\", \"1\");\n                    if (!params.get(\"pageSize\")) params.set(\"pageSize\", \"50\");\n                    let url = \"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.trackSheets_routes.GETALL_TRACK_SHEETS, \"/\").concat((_selectedClients_1 = selectedClients[0]) === null || _selectedClients_1 === void 0 ? void 0 : _selectedClients_1.value, \"?\").concat(params.toString());\n                    if (warningFilter !== null) {\n                        url += \"&systemGeneratedWarnings=\".concat(warningFilter);\n                    }\n                    const trackSheetResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(url);\n                    setTrackSheetData({\n                        ...trackSheetResponse,\n                        data: mapCustomFields(trackSheetResponse.data || [])\n                    });\n                } catch (error) {\n                    setCustomFieldsMap({});\n                    setTrackSheetData({\n                        data: [],\n                        datalength: 0\n                    });\n                }\n            } else {\n                setCustomFieldsMap({});\n                setTrackSheetData({\n                    data: [],\n                    datalength: 0\n                });\n            }\n        };\n        fetchData();\n    }, [\n        selectedClients,\n        searchParams,\n        customFieldsReloadTrigger,\n        deleteData,\n        warningFilter\n    ]);\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const selectedClientObj = client === null || client === void 0 ? void 0 : client.find((c)=>{\n        var _c_id, _selectedClients_;\n        return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === ((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value);\n    });\n    const showLegrandColumns = (selectedClientObj === null || selectedClientObj === void 0 ? void 0 : selectedClientObj.client_name) === \"LEGRAND\";\n    const showOrcaColumns = (selectedClientObj === null || selectedClientObj === void 0 ? void 0 : selectedClientObj.associate.name) === \"ORCA\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__.FancySelect, {\n                            frameworks: clientOptions,\n                            selected: selectedClients,\n                            setSelected: handleClientChange,\n                            label: \"Select Client\",\n                            placeholder: \"Search clients...\",\n                            className: \"max-w-xs\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExportTrackSheet__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        filteredTrackSheetData: trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.data,\n                        customFieldsMap: customFieldsMap,\n                        columnVisibility: columnVisibility,\n                        showOrcaColumns: showOrcaColumns,\n                        warningFilter: warningFilter\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    permissions: permissions,\n                    totalPages: totalPages,\n                    customFieldsMap: customFieldsMap,\n                    selectedClients: selectedClients,\n                    trackSheetData: trackSheetData,\n                    pageSize: pageSize,\n                    carrierDataUpdate: carrierDataUpdate,\n                    clientDataUpdate: clientDataUpdate,\n                    setColumnVisibility: setColumnVisibility,\n                    userData: userData,\n                    showOrcaColumns: showOrcaColumns,\n                    showLegrandColumns: showLegrandColumns\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientSelectPage, \"F8I2RH5/RXrb9SEjbA8xeGlO9Og=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams\n    ];\n});\n_c = ClientSelectPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientSelectPage);\nvar _c;\n$RefreshReg$(_c, \"ClientSelectPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx\n"));

/***/ })

});