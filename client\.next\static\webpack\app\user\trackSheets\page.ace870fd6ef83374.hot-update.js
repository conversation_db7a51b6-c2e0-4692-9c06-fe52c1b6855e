"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/ManageTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTracksheet_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTracksheet/createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/createTracksheet/createTrackSheet.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _TrackSheetTabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TrackSheetTabs */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetTabs.tsx\");\n/* harmony import */ var _ImportModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ImportModal */ \"(app-pages-browser)/./app/user/trackSheets/ImportModal.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, associate, userData, actions, carrierDataUpdate, clientDataUpdate, carrier } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    const [customFieldsReloadTrigger, setCustomFieldsReloadTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isImportModalOpen, setImportModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAssociateId, setSelectedAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [warningFilter, setWarningFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const associateOptions = (associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    })) || [];\n    const clientOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!selectedAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === selectedAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    }, [\n        client,\n        selectedAssociateId\n    ]);\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            setCustomFieldsReloadTrigger,\n            customFieldsReloadTrigger,\n            warningFilter,\n            setWarningFilter\n        }), [\n        filterdata,\n        setFilterData,\n        deleteData,\n        setDeletedData,\n        setCustomFieldsReloadTrigger,\n        customFieldsReloadTrigger,\n        warningFilter,\n        setWarningFilter\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_3__.TrackSheetContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetTabs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                activeView: activeView,\n                                setActiveView: setActiveView,\n                                isImportModalOpen: isImportModalOpen,\n                                setImportModalOpen: setImportModalOpen,\n                                selectedAssociateId: selectedAssociateId,\n                                setSelectedAssociateId: setSelectedAssociateId,\n                                selectedClientId: selectedClientId,\n                                setSelectedClientId: setSelectedClientId,\n                                associateOptions: associateOptions,\n                                clientOptions: clientOptions\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImportModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                isOpen: isImportModalOpen,\n                                onClose: ()=>setImportModalOpen(false),\n                                userData: userData\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTracksheet_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                client: client,\n                                associate: associate,\n                                userData: userData,\n                                activeView: activeView,\n                                setActiveView: setActiveView,\n                                permissions: actions,\n                                carrierDataUpdate: carrierDataUpdate,\n                                clientDataUpdate: clientDataUpdate,\n                                carrier: carrier,\n                                associateId: selectedAssociateId,\n                                clientId: selectedClientId\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    permissions: actions,\n                                    client: client,\n                                    clientDataUpdate: clientDataUpdate,\n                                    carrierDataUpdate: carrierDataUpdate,\n                                    userData: userData\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"xxclkiAMH4HSeJPlnp5KbfHvEZY=\");\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx\n"));

/***/ })

});