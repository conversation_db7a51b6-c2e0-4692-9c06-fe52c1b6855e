export function getOrderBy(
  sortByArr: string[],
  orderArr: string[],
  allowedFields: string[]
) {
  if (!allowedFields.length) {
    throw new Error("No allowed fields defined for sorting.");
  }

  if (!Array.isArray(sortByArr)) sortByArr = [sortByArr];
  if (!Array.isArray(orderArr)) orderArr = [orderArr];

  return sortByArr.map((rawField, idx) => {
    const inputField = rawField?.trim().toLowerCase();
    const sortOrder = orderArr[idx]?.toLowerCase() === "desc" ? "desc" : "asc";

    // Find matching allowed field in a case-insensitive way
    const matchedField =
      allowedFields.find((f) => f.toLowerCase() === inputField) || allowedFields[0];

    if (matchedField.includes(".")) {
      const [relation, nestedField] = matchedField.split(".");
      return {
        [relation]: {
          [nestedField]: sortOrder,
        },
      };
    }

    return { [matchedField]: sortOrder };
  });
}
