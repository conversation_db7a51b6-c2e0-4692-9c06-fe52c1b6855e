import React from "react";
import AddCarrier from "@/app/pms/manage_carrier/addCarrier";
import ViewCarrier from "./ViewCarrier";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  employee_routes,
  search_routes,
} from "@/lib/routePath";
import UpdateCarrier from "./UpdateCarrier";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const CarrierPage = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
   name: string;
   carrier_code: string;
   carrier_2nd_name: string;
   createdBy:string,
       sortBy?: string;
    order?: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    name,
    carrier_code,
    carrier_2nd_name,
    createdBy,
     sortBy,
    order
  } = searchParams;
  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (name) params.append("name", name);
  if (carrier_code) params.append("carrier_code", carrier_code);
  if (carrier_2nd_name) params.append("carrier_2nd_name", carrier_2nd_name);
  if (createdBy) params.append("createdBy",createdBy)
  if (sortBy) params.append("sortBy", sortBy);
  if (order) params.append("order", order);
  params.append("includeRelations", "false");
  const apiUrl = `${search_routes.GET_SEARCH}/Carrier?${params.toString()}`;
  
  // const apiUrl = `${carrier_routes.GETALL_CARRIER}?${params.toString()}`;
  console.time("Time taken for: " + apiUrl);
  const allCarrier = await getAllData(apiUrl);
  console.timeEnd("Time taken for: " + apiUrl);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);

  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  //  ("User Permissions:", userPermissions);

  const corporationCookie = await getCookie("corporationtoken");
  //  ("Corporation Cookie:", corporationCookie);

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  //  ("Final Permissions Array:", permissions);

  return (
    <div className="w-full p-2 pl-4">
      <div className="h-9 flex items-center">
        <AdminNavBar link={"/pms/manage_carrier"} name={"Manage Carrier"} />
      </div>
      <div className="space-y-2">
        <h1 className="text-2xl">Manage Carrier</h1>
        <p className="text-sm text-gray-700">Here You Can Manage Carrier</p>
      </div>
      <div className="w-full">
        <div className="flex justify-end">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["create-carrier"]}
          >
            <AddCarrier data={allCarrier} params={params} userData={userData} />
          </PermissionWrapper>
        </div>
        <div className="w-full py-4 animate-in fade-in duration-1000">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["view-carrier"]}
          >
            <ViewCarrier alldata={allCarrier} permissions={permissions} />
          </PermissionWrapper>
        </div>
      </div>
    </div>
  );
};

export default CarrierPage;