import React, { useEffect, useState } from "react";

import { AddTask } from "./AddTask";
import { SelectClient } from "./SelectClient";
import { Car } from "lucide-react";
import ViewWorkRecord from "./ViewWorkRecord";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { getAllData, getCookie } from "@/lib/helpers";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import { NavBar } from "@/app/_component/NavBar";
import Parent from "./parent";
import Manage from "./manage";
import { useSearchParams } from "next/navigation";

const Page = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    date_from?: string;
    date_to: string;
    username: string;
    clientname: string;
    carriername: string;
    work_type: string;
    category: string;
    task_type: string;
    // StartTime: string;
    // EndTime: string;
    Workstatus: string;
    // TimeSpent: string;
    actual_number: string;
    notes: string;
    sortBy?: string;
    order?: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    date_from,
    date_to,
    username,
    clientname,
    carriername,
    work_type,
    category,
    task_type,
    // StartTime,
    // EndTime,
    Workstatus,
    // TimeSpent,
    actual_number,
    notes,
    sortBy,
    order
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (username) params.append("username", username);
  if (clientname) params.append("clientname", clientname);
  if (carriername) params.append("carriername", carriername);
  if (work_type) params.append("work_type", work_type);
  if (category) params.append("category", category);
  if (task_type) params.append("task_type", task_type);
  // if (StartTime) params.append("StartTime", StartTime);
  // if (EndTime) params.append("EndTime", EndTime);
  if (Workstatus) params.append("Workstatus", Workstatus);
  // if (TimeSpent) params.append("TimeSpent", TimeSpent);
  if (actual_number) params.append("actual_number", actual_number);
  if (notes) params.append("notes", notes);
  if (date_from && date_to) {
    params.append("date_from", date_from);
    params.append("date_to", date_to);
  }
  if (sortBy) params.append("sortBy", sortBy);
  if (order) params.append("order", order);
  const apiUrl = `${
    workreport_routes.GET_CURRENT_USER_WORKREPORT
  }?${params.toString()}`;

  // Start measuring time
  console.time("Time taken for: " + apiUrl);
  const WorkData = await getAllData(apiUrl);
  const pageSizes = 100;
  const pages = 1;
  console.timeEnd("Time taken for: " + apiUrl);

  // End measuring time and log the result
  const workTypes = await getAllData(worktype_routes.GETALL_WORKTYPE);
  const allClient = await getAllData(
    `${client_routes.GETALL_CLIENT}?pageSize=${pageSizes}&page=${pages}`
  );
  const allUser = await getAllData(employee_routes.GETALL_USERS);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions = userData?.role?.role_permission || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  const actions = permissions?.map((item) => item?.permission?.action);

  return (
    <>
      <Manage
        permissions={permissions}
        userData={userData}
        WorkData={WorkData}
        workTypes={workTypes}
        allClient={allClient?.data}
        actions={actions}
        params={params}
        allUser={allUser?.data}
      />
    </>
  );
};

export default Page;
