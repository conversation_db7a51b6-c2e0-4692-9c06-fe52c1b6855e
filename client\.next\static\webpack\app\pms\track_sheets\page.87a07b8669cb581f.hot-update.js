"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/track_sheets/page",{

/***/ "(app-pages-browser)/./app/pms/track_sheets/ManageWorkSheet.tsx":
/*!**************************************************!*\
  !*** ./app/pms/track_sheets/ManageWorkSheet.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/pms/track_sheets/TrackSheetContext.tsx\");\n/* harmony import */ var _components_adminNavBar_adminNavBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/adminNavBar/adminNavBar */ \"(app-pages-browser)/./components/adminNavBar/adminNavBar.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx\");\n/* harmony import */ var _lib_permissionWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/permissionWrapper */ \"(app-pages-browser)/./lib/permissionWrapper.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrierDataUpdate, clientDataUpdate } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customFieldsReloadTrigger, setCustomFieldsReloadTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [warningFilter, setWarningFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            setCustomFieldsReloadTrigger,\n            customFieldsReloadTrigger,\n            warningFilter,\n            setWarningFilter\n        }), [\n        filterdata,\n        setFilterData,\n        deleteData,\n        setDeletedData,\n        setCustomFieldsReloadTrigger,\n        customFieldsReloadTrigger,\n        warningFilter,\n        setWarningFilter\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_2__.TrackSheetContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"ml-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-9 flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_adminNavBar_adminNavBar__WEBPACK_IMPORTED_MODULE_3__.AdminNavBar, {\n                        link: \"/pms/track_sheets\",\n                        name: \"TrackSheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 mt-2 \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl\",\n                        children: \"TrackSheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_permissionWrapper__WEBPACK_IMPORTED_MODULE_5__.PermissionWrapper, {\n                    permissions: permissions,\n                    requiredPermissions: [\n                        \"View-TrackSheet\"\n                    ],\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full  animate-in fade-in duration-1000 mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            permissions: permissions,\n                            client: client,\n                            carrierDataUpdate: carrierDataUpdate,\n                            clientDataUpdate: clientDataUpdate\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ManageWorkSheet.tsx\",\n        lineNumber: 31,\n        columnNumber: 8\n    }, undefined);\n};\n_s(ManageWorkSheet, \"TD3N6BKolTFcXRAFP3AFcvOFDPE=\");\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/track_sheets/ManageWorkSheet.tsx\n"));

/***/ })

});