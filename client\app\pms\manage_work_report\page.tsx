"use server";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  category_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import React from "react";
import ViewWorkReport from "./ViewWorkReport";
import ExportReport from "./ExportReport";
import ManageWorkReport from "./ManageWorkReport";
// import CreateWorkReport from './CreateWorkReport'

const page = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    date_from?: string;
    date_to: string;
    search: string;
    client_name: string;
    carriername: string;
    user: string;
    work_type: string;
    category: string;
    task_type: string;
    // StartTime: string;
    // EndTime: string;
    Workstatus: string;
    // TimeSpent: string;
    ActualNumber: string;
    actual_number: string;
    notes: string;
    switch_type: string;
    sortBy?: string;
    order?: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    date_from,
    date_to,
    search,
    client_name,
    carriername,
    user,
    work_type,
    category,
    task_type,
    // StartTime,
    // EndTime,
    Workstatus,
    // TimeSpent,
    actual_number,
    notes,
    switch_type,
    sortBy,
    order
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (client_name) params.append("client_name", client_name);
  if (carriername) params.append("carriername", carriername);
  if (work_type) params.append("work_type", work_type);
  if (category) params.append("category", category);
  if (task_type) params.append("task_type", task_type);
  if (switch_type) params.append("switch_type", switch_type);
  if (user) params.append("user", user);
  // if (StartTime) params.append("StartTime", StartTime);
  // if (EndTime) params.append("EndTime", EndTime);
  if (Workstatus) params.append("Workstatus", Workstatus);
  // if (TimeSpent) params.append("TimeSpent", TimeSpent);
  if (actual_number) params.append("actual_number", actual_number);
  if (notes) params.append("notes", notes);
  if (date_from && date_to) {
    params.append("date_from", date_from);
    params.append("date_to", date_to);
  }
  if (sortBy) params.append("sortBy", sortBy);
  if (order) params.append("order", order);
  const apiUrl = `${workreport_routes.GETALL_WORKREPORT}?${params.toString()}`;
  const data = await getAllData(apiUrl);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <ManageWorkReport data={data} permissions={permissions} params={params} />
    </>
  );
};

export default page;
