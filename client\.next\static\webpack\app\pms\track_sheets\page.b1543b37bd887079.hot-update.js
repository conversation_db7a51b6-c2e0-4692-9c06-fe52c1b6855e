"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/track_sheets/page",{

/***/ "(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx":
/*!***************************************************!*\
  !*** ./app/pms/track_sheets/ClientSelectPage.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/_component/FancySelect */ \"(app-pages-browser)/./app/_component/FancySelect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ExportTrackSheet__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ExportTrackSheet */ \"(app-pages-browser)/./app/pms/track_sheets/ExportTrackSheet.tsx\");\n/* harmony import */ var _ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ViewTrackSheet */ \"(app-pages-browser)/./app/pms/track_sheets/ViewTrackSheet.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ClientSelectPage = (param)=>{\n    let { permissions, client, carrierDataUpdate, clientDataUpdate, userData } = param;\n    _s();\n    const [customFieldsMap, setCustomFieldsMap] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedClients, setSelectedClients] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trackSheetData, setTrackSheetData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        data: [],\n        datalength: 0\n    });\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const pageSize = parseInt(searchParams.get(\"pageSize\")) || 50;\n    const totalPages = Math.ceil(((trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.datalength) || 0) / pageSize);\n    const params = new URLSearchParams(searchParams);\n    const clientOptions = (client === null || client === void 0 ? void 0 : client.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.client_name\n        };\n    })) || [];\n    const mapCustomFields = (data)=>data.map((row)=>({\n                ...row,\n                customFields: (row.TrackSheetCustomFieldMapping || []).reduce((acc, mapping)=>{\n                    acc[mapping.customFieldId] = mapping.value;\n                    return acc;\n                }, {})\n            }));\n    const handleClientChange = (newSelectedClients)=>{\n        setSelectedClients(newSelectedClients);\n    };\n    // const  {customFieldsReloadTrigger} = useContext()\n    const { customFieldsReloadTrigger, deleteData, warningFilter } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            if (selectedClients.length > 0) {\n                try {\n                    var _selectedClients_, _selectedClients_1;\n                    const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value));\n                    const fieldsMap = {};\n                    (response.custom_fields || []).forEach((field)=>{\n                        fieldsMap[field.id] = {\n                            name: field.name,\n                            type: field.type\n                        };\n                    });\n                    setCustomFieldsMap(fieldsMap);\n                    if (!params.get(\"page\")) params.set(\"page\", \"1\");\n                    if (!params.get(\"pageSize\")) params.set(\"pageSize\", \"50\");\n                    let url = \"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.trackSheets_routes.GETALL_TRACK_SHEETS, \"/\").concat((_selectedClients_1 = selectedClients[0]) === null || _selectedClients_1 === void 0 ? void 0 : _selectedClients_1.value, \"?\").concat(params.toString());\n                    if (warningFilter !== null) {\n                        url += \"&systemGeneratedWarnings=\".concat(warningFilter);\n                    }\n                    const trackSheetResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(url);\n                    setTrackSheetData({\n                        ...trackSheetResponse,\n                        data: mapCustomFields(trackSheetResponse.data || [])\n                    });\n                } catch (error) {\n                    setCustomFieldsMap({});\n                    setTrackSheetData({\n                        data: [],\n                        datalength: 0\n                    });\n                }\n            } else {\n                setCustomFieldsMap({});\n                setTrackSheetData({\n                    data: [],\n                    datalength: 0\n                });\n            }\n        };\n        fetchData();\n    }, [\n        selectedClients,\n        searchParams,\n        customFieldsReloadTrigger,\n        deleteData,\n        warningFilter\n    ]);\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const selectedClientObj = client === null || client === void 0 ? void 0 : client.find((c)=>{\n        var _c_id, _selectedClients_;\n        return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === ((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value);\n    });\n    const showLegrandColumns = (selectedClientObj === null || selectedClientObj === void 0 ? void 0 : selectedClientObj.client_name) === \"LEGRAND\";\n    const showOrcaColumns = (selectedClientObj === null || selectedClientObj === void 0 ? void 0 : selectedClientObj.associate.name) === \"ORCA\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__.FancySelect, {\n                            frameworks: clientOptions,\n                            selected: selectedClients,\n                            setSelected: handleClientChange,\n                            label: \"Select Client\",\n                            placeholder: \"Search clients...\",\n                            className: \"max-w-xs\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExportTrackSheet__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        filteredTrackSheetData: trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.data,\n                        customFieldsMap: customFieldsMap,\n                        columnVisibility: columnVisibility,\n                        showOrcaColumns: showOrcaColumns,\n                        warningFilter: warningFilter\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    permissions: permissions,\n                    totalPages: totalPages,\n                    customFieldsMap: customFieldsMap,\n                    selectedClients: selectedClients,\n                    trackSheetData: trackSheetData,\n                    pageSize: pageSize,\n                    carrierDataUpdate: carrierDataUpdate,\n                    clientDataUpdate: clientDataUpdate,\n                    setColumnVisibility: setColumnVisibility,\n                    userData: userData,\n                    showOrcaColumns: showOrcaColumns,\n                    showLegrandColumns: showLegrandColumns\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientSelectPage, \"F8I2RH5/RXrb9SEjbA8xeGlO9Og=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams\n    ];\n});\n_c = ClientSelectPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientSelectPage);\nvar _c;\n$RefreshReg$(_c, \"ClientSelectPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx\n"));

/***/ })

});