import { isValid, parse } from "date-fns";
import { handleError } from "../../../utils/helpers";
import { getVisibility } from "../visibilty/visibiltyHelper";
import { getOrderBy } from "../../../utils/sortHelper";

//     const whereClause: { AND?: { date: { gte?: Date; lte?: Date } } } = {};

//     if (from && to) {
//       whereClause.AND = {
//         date: {
//           gte: from,
//           lte: to,
//         },
//       };
//     } else if (from) {
//       whereClause.AND.date = {
//         gte: from,
//       };
//     } else if (to) {
//       whereClause.AND.date = {
//         lte: to,
//       };
//     }

// controllers/workreportController.js
export const viewWorkreport = async (req, res) => {
  try {
    const {
      date_from,
      date_to,
      page,
      pageSize,
      client_name,
      carriername,
      user,
      work_type,
      category,
      task_type,
      Workstatus,
      actual_number,
      notes,
      switch_type,
      sortBy = "id",
      order = "desc"
    } = req.query;

    console.log(req.query,'req query');
    

    const taskTypeMap = {
      BACKLOG: "BACKLOG",
      REGULAR: "REGULAR",
    };

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);

    const from = date_from ? new Date(date_from) : null;
    const to = date_to ? new Date(date_to) : null;

    if (to) {
      to.setHours(23, 59, 59, 999); // Set end of day
    }

    const whereClause: { AND?: any[] } = { AND: [] };

    // Handle Date Range
    if (from || to) {
      const dateCondition: any = { date: {} };
      if (from) dateCondition.date.gte = from;
      if (to) dateCondition.date.lte = to;
      whereClause.AND.push(dateCondition);
    }

    // Search conditions for filtering
    const searchConditions: any[] = [];

    if (client_name) {
      const clientList = client_name.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: clientList.map((clientName) => ({
          client: {
            client_name: {
              contains: clientName,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (carriername) {
      const carrierList = carriername.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: carrierList.map((carrier) => ({
          carrier: {
            name: {
              contains: carrier,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (user) {
      const userList = user.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: userList.map((user) => ({
          user: {
            username: {
              contains: user,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (work_type) {
      const workList = work_type.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: workList.map((work) => ({
          work_type: {
            work_type: {
              contains: work,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (category) {
      const categoryList = category.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: categoryList.map((category) => ({
          category: {
            category_name: {
              contains: category,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (task_type) {
      const typeList = task_type.split(",").map((item) => item.trim());
      const normalizedType = task_type.toUpperCase();
      searchConditions.push({
        OR: typeList.map((type) => ({
          task_type: {
            in: Object.values(taskTypeMap).filter((value) =>
              value.toLowerCase().includes(type.toLowerCase())
            ),
          },
        })),
      });
    }

    if(switch_type) {
      const normalizedSwitchType = switch_type.toUpperCase();
      const switchTypeMap = {
        INT: "INT",
        EXT: "EXT",
      };

      searchConditions.push({
        switch_type: {
          in: Object.values(switchTypeMap).filter((value) =>
            value.toLowerCase().includes(normalizedSwitchType.toLowerCase())
          ),
        },
      });
    }
    
    if (Workstatus) {
      const normalizedWorkStatus = Workstatus.toUpperCase();
      const workStatusMap = {
        STARTED: "STARTED",
        PAUSED: "PAUSED",
        RESUMED: "RESUMED",
        FINISHED: "FINISHED",
      };

      searchConditions.push({
        work_status: {
          in: Object.values(workStatusMap).filter((value) =>
            value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
          ),
        },
      });
    }

    if (actual_number) {
      const actualNumberList = actual_number.split(",")
        .map((item) => parseInt(item.trim(), 10))
        .filter((num) => !isNaN(num)); // Remove any invalid numbers
    
      if (actualNumberList.length > 0) {
        searchConditions.push({
          actual_number: {
            in: actualNumberList, // Matches any of the values
          },
        });
      }
    }
    

    if (notes) {
      const notesList = notes.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: notesList.map((note) => ({
          notes: {
            contains: note,
            mode: "insensitive",
          },
        })),
      });
    }
    // if (Client) {
    //   const clientList = Client.split(',').map(item => item.trim());

    //   searchConditions.push({
    //     OR: clientList.map(clientName => ({
    //       client: {
    //         client_name: {
    //           contains: clientName,
    //           mode: "insensitive",
    //         },
    //       },
    //     })),
    //   });
    // }

    // Combine search conditions into the whereClause
    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions, // Add all the conditions under OR
      });
    }

    // Sorting setup
    const fieldMapping = {
      stableId: "stableId",
      client: "client.client_name",
      carrier: "carrier.name",
      user: "user.username",
      work_type: "work_type.work_type",
      category: "category.category_name",
    };
    const sortByArr = String(sortBy).split(",").map((s) => s.trim());
    const orderArr = String(order).split(",").map((s) => s.trim());
    const mappedSortByArr = sortByArr.map((field) => fieldMapping[field] || field);
    const allowedFields = [
      "id",
      "date",
      "work_status",
      "actual_number",
      "start_time",
      "finish_time",
      "time_spent",
      "task_type",
      "notes",
      "client.client_name",
      "carrier.name",
      "user.username",
      "work_type.work_type",
      "category.category_name",
      "stableId"
    ];
    const orderBy = getOrderBy(mappedSortByArr, orderArr, allowedFields);

    // Fetching data with filters and pagination
    const data = await prisma.workReport.findMany({
      where: whereClause,
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      include: {
        user: true,
        client: true,
        carrier: true,
        work_type: true,
        category: true,
      },
      orderBy,
    });

    // Counting total rows to support pagination
    const datalength = await prisma.workReport.count({
      where: whereClause,
    });

    // Return data if available
    if (data.length > 0) {
      return res.status(200).json({ data, datalength });
    }

    // Return empty response if no data is found
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

// export const viewWorkreport = async (req, res) => {
//   try {
//     const {
//       fDate,
//       tDate,
//       Client,
//       Carrier,
//       Username,
//       Work,
//       Category,
//       Type,
//       Workstatus,
//       ActualNumber,
//       Notes,
//       page,
//       pageSize,
//     } = req.query;

//     const taskTypeMap = {
//       BACKLOG: "BACKLOG",
//       REGULAR: "REGULAR",
//     };

//     const from = fDate ? new Date(fDate) : null;
//     const to = tDate ? new Date(tDate) : null;

//     if (to) {
//       to.setHours(23, 59, 59, 999); // End of the day
//     }

//     // Pagination: skip is the offset, take is the number of items per page
//     const skip = (Number(page) - 1) * Number(pageSize);
//     const take = Number(pageSize);

//     // Initialize the whereClause as an empty object
//     const whereClause = { AND: [] };

//     // Add date range filter if from or to date is specified
//     if (from || to) {
//       const dateCondition: any = { date: {} };
//       if (from) dateCondition.date.gte = from;
//       if (to) dateCondition.date.lte = to;
//       whereClause.AND.push(dateCondition);
//     }

//     // Define the search conditions for various fields
//     const searchConditions = [];

//     if (Client) {
//       searchConditions.push({
//         client: {
//           client_name: {
//             contains: Client,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Carrier) {
//       searchConditions.push({
//         carrier: {
//           name: {
//             contains: Carrier,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Username) {
//       searchConditions.push({
//         user: {
//           username: {
//             contains: Username,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Work) {
//       searchConditions.push({
//         work_type: {
//           work_type: {
//             contains: Work,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Category) {
//       searchConditions.push({
//         category: {
//           category_name: {
//             contains: Category,
//             mode: "insensitive",
//           },
//         },
//       });
//     }

//     if (Type) {
//       const normalizedType = Type.toUpperCase();
//       searchConditions.push({
//         task_type: {
//           in: Object.values(taskTypeMap).filter((value) =>
//             value.toLowerCase().includes(normalizedType.toLowerCase())
//           ),
//         },
//       });
//     }

//     if (Workstatus) {
//       const normalizedWorkStatus = Workstatus.toUpperCase();
//       const workStatusMap = {
//         STARTED: "STARTED",
//         PAUSED: "PAUSED",
//         RESUMED: "RESUMED",
//         FINISHED: "FINISHED",
//       };

//       searchConditions.push({
//         work_status: {
//           in: Object.values(workStatusMap).filter((value) =>
//             value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
//           ),
//         },
//       });
//     }

//     if (ActualNumber) {
//       const actualNumberInt = parseInt(ActualNumber, 10);
//       if (!isNaN(actualNumberInt)) {
//         searchConditions.push({
//           actual_number: actualNumberInt,
//         });
//       }
//     }

//     if (Notes) {
//       searchConditions.push({
//         notes: {
//           contains: Notes,
//           mode: "insensitive",
//         },
//       });
//     }

//     // Combine all search conditions
//     if (searchConditions.length > 0) {
//       whereClause.AND.push({ AND: searchConditions });
//     }

//     // Fetch data from the database with pagination and filtering
//     const data = await prisma.workReport.findMany({
//       where: whereClause,
//       take: take,
//       skip: skip,
//       include: {
//         user: true,
//         client: true,
//         carrier: true,
//         work_type: true,
//         category: true,
//       },
//       orderBy: {
//         id: "desc", // Ordering by ID descending, change as needed
//       },
//     });

//     // Get total row count for pagination (with applied filters)
//     const totalRows = await prisma.workReport.count({
//       where: whereClause,
//     });

//     // Return paginated data and total rows
//     return res.status(200).json({
//       data,
//       totalRows,
//     });
//   } catch (error) {
//     console.error("Error fetching work report:", error);
//     return res.status(500).json({ error: "Server error" });
//   }
// };

export const viewclientWorkreport = async (req, res) => {
  try {
    const client_name = req.body.client_name;

    if (!client_name) {
      return res.status(400).json({ message: "Client name is required" });
    }

    const client = await prisma.client.findFirst({
      where: { client_name: client_name },
    });

    if (!client) {
      return res.status(404).json({ message: "Client not found" });
    }

    const data = await prisma.workReport.findMany({
      where: { client_id: client.id },
      include: {
        user: true,
        carrier: true,
        work_type: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data: data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this client" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewuserWorkreport = async (req, res) => {
  try {
    const username = req.body.username;

    if (!username) {
      return res.status(400).json({ message: "user name is required" });
    }

    const user = await prisma.user.findFirst({
      where: { username: username },
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const data = await prisma.workReport.findMany({
      where: { user_id: user.id },
      include: {
        client: true,
        carrier: true,
        work_type: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data: data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this user" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewworktypeWorkreport = async (req, res) => {
  try {
    const work_type = req.body.work_type;

    if (!work_type) {
      return res.status(400).json({ message: "work type is required" });
    }

    const workType = await prisma.workType.findFirst({
      where: { work_type: work_type },
    });

    if (!workType) {
      return res.status(404).json({ message: "Work type not found" });
    }

    const data = await prisma.workReport.findMany({
      // where: { work_type : workType.work_type },
      where: {
        work_type: {
          work_type: work_type,
        },
      },
      include: {
        client: true,
        carrier: true,
        user: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data: data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this workType" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewcategoryWorkreport = async (req, res) => {
  try {
    const { category } = req.body;

    const validCategories = ["AUDIT", "ENTRY", "REPORT"];
    if (!category || !validCategories.includes(category)) {
      return res.status(400).json({ message: "Valid category is required" });
    }

    const data = await prisma.workReport.findMany({
      where: { category: category },
      include: {
        user: true,
        client: true,
        carrier: true,
        work_type: true,
      },
    });

    if (data.length > 0) {
      return res.status(200).json({ success: true, data });
    }

    return res
      .status(404)
      .json({ message: "No work reports found for this category" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const getCurrentUserWorkReport = async (req, res) => {
  try {
    const {
      date_from,
      date_to,
      page,
      pageSize,
      username,
      clientname,
      carriername,
      work_type,
      category,
      task_type,
      // StartTime,
      // EndTime,
      Workstatus,
      // TimeSpent,
      actual_number,
      notes,
      sortBy = "id",
      order = "desc"
    } = req.query;
console.log(req.query,'query');

    const taskTypeMap = {
      BACKLOG: "BACKLOG",
      REGULAR: "REGULAR",
    };

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);

    const from = date_from ? new Date(date_from) : null;
    const to = date_to ? new Date(date_to) : null;
    if (to) {
      to.setHours(23, 59, 59, 999);
    }

    const whereClause: { AND?: any[] } = { AND: [] };

    if (from || to) {
      const dateCondition: any = { date: {} };
      if (from) dateCondition.date.gte = from;
      if (to) dateCondition.date.lte = to;
      whereClause.AND.push(dateCondition);
    }

    const searchConditions: any[] = [];

    if (clientname) {
      const clientList = clientname.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: clientList.map((clientName) => ({
          client: {
            client_name: {
              contains: clientName,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (carriername) {
      const carrierList = carriername.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: carrierList.map((carrier) => ({
          carrier: {
            name: {
              contains: carrier,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (username) {
      const userList = username.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: userList.map((user) => ({
          user: {
            username: {
              contains: user,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (work_type) {
      const workTypeList = work_type.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: workTypeList.map((workType) => ({
          work_type: {
            work_type: {
              contains: workType,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (category) {
      const categoryList = category.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: categoryList.map((category) => ({
          category: {
            category_name: {
              contains: category,
              mode: "insensitive",
            },
          },
        })),
      });
    }
    // if (Type) {
    //   searchConditions.push({
    //     task_type: {
    //       equals: Type,
    //     },
    //   });
    // }

    if (task_type) {
      const typeList = task_type.split(",").map((item) => item.trim());
      const normalizedType = task_type.toUpperCase();
      searchConditions.push({
        OR: typeList.map((type) => ({
          task_type: {
            in: Object.values(taskTypeMap).filter((value) =>
              value.toLowerCase().includes(type.toLowerCase())
            ),
          },
        })),
      });
    }

    // if (StartTime) {
    //   const [hour, minute, period] = StartTime.split(':');
    //   const timeInt = parseInt(hour, 10);

    //   const adjustedHour = (period === 'PM' && timeInt !== 12) ? timeInt + 12 : timeInt;

    //   const targetStartTime = new Date();
    //   targetStartTime.setHours(adjustedHour, minute, 0, 0);

    //   const endStartTime = new Date(targetStartTime);
    //   endStartTime.setMinutes(targetStartTime.getMinutes() + 59);

    //   searchConditions.push({
    //     start_time: {
    //       gte: targetStartTime,
    //       lte: endStartTime,
    //     },
    //   });
    // }

    // if (EndTime) {
    //   searchConditions.push({
    //     finish_time: {
    //       contains: EndTime,
    //       mode: "insensitive",
    //     },
    //   });
    // }

    // if (Workstatus) {
    //   searchConditions.push({
    //     work_status: {
    //       equals: Workstatus,
    //     },
    //   });
    // }
    if (Workstatus) {
      const normalizedWorkStatus = Workstatus.toUpperCase();

      const workStatusMap = {
        STARTED: "STARTED",
        PAUSED: "PAUSED",
        RESUMED: "RESUMED",
        FINISHED: "FINISHED",
      };

      searchConditions.push({
        work_status: {
          in: Object.values(workStatusMap).filter((value) =>
            value.toLowerCase().includes(normalizedWorkStatus.toLowerCase())
          ),
        },
      });
    }

    // if (TimeSpent) {
    //   searchConditions.push({
    //     time_spent: {
    //       contains: TimeSpent,
    //       mode: "insensitive",
    //     },
    //   });
    // }

    // if (ActualNumber) {
    //   searchConditions.push({
    //     actual_number: {
    //       contains: ActualNumber,
    //       mode: "insensitive",
    //     },
    //   });
    // }

    if (actual_number) {
      const actualNumberList = actual_number.split(",")
        .map((item) => parseInt(item.trim(), 10))
        .filter((num) => !isNaN(num)); // Remove any invalid numbers
    
      if (actualNumberList.length > 0) {
        searchConditions.push({
          actual_number: {
            in: actualNumberList, // Matches any of the values
          },
        });
      }
    }
    if (notes) {
      const notesList = notes.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: notesList.map((note) => ({
          notes: {
            contains: note,
            mode: "insensitive",
          },
        })),
      });
    }

    // If any search condition exists (Client, Carrier, or search), push them under OR
    // if (searchConditions.length > 0) {
    whereClause.AND.push({
      AND: searchConditions, // Add all the conditions under OR
    });
    // }
    // Get the current user's ID and check visibility
    const userId = req.user_id;
    const user_ids = await getVisibility(userId, "WorkReport");
    if (!Array.isArray(user_ids)) {
      return res.status(400).json({ message: "Invalid user_ids format" });
    }
    const userIdArray = [...new Set(user_ids.map((u) => u.user_id))];

    // Sorting setup
    const fieldMapping = {
      client: "client.client_name",
      carrier: "carrier.name",
      user: "user.username",
      work_type: "work_type.work_type",
      category: "category.category_name",
    };
    const sortByArr = String(sortBy).split(",").map((s) => s.trim());
    const orderArr = String(order).split(",").map((s) => s.trim());
    const mappedSortByArr = sortByArr.map((field) => fieldMapping[field] || field);
    const allowedFields = [
      "id",
      "date",
      "work_status",
      "actual_number",
      "start_time",
      "finish_time",
      "time_spent",
      "task_type",
      "notes",
      "client.client_name",
      "carrier.name",
      "user.username",
      "work_type.work_type",
      "category.category_name",
    ];
    const orderBy = getOrderBy(mappedSortByArr, orderArr, allowedFields);

    const data = await prisma.workReport.findMany({
      where: {
        ...whereClause,
        user_id: { in: userIdArray }, // Filter records where user_id is in userIdArray
      },
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      select: {
        id: true,
        work_status: true,
        date: true,
        client: true,
        carrier: true,
        work_type: true,
        user: true,
        category: true,
        planning_nummbers: true,
        expected_time: true,
        actual_number: true,
        start_time: true,
        finish_time: true,
        time_spent: true,
        task_type: true,
        notes: true,
        pause: true,
        resume: true,
        carrier_id: true,
        client_id: true,
        work_type_id: true,
      },
      orderBy,
    });

    // Get total count for pagination
    const datalength = await prisma.workReport.count({
      where: {
        user_id: { in: userIdArray },
        ...whereClause,
      },
    });

    // Return the data and total count
    if (data.length > 0) {
      return res.status(200).json({ data, datalength });
    } else {
      return res.status(400).json([]); // Return empty array if no data found
    }
  } catch (error) {
    return handleError(res, error); // Handle any errors
  }
};

export const getCurrentUserWorkReportStatusCount = async (req, res) => {
  try {
    const userId = req.user_id;
    const userIds = await getVisibility(userId, "WorkReport");

    if (!Array.isArray(userIds)) {
      return res.status(400).json({ message: "Invalid user_ids format" });
    }

    const userIdArray = [...new Set(userIds.map((u) => u.user_id))];

    const completedTasksCount = await prisma.workReport.count({
      where: {
        user_id: { in: userIdArray },
        work_status: "FINISHED",
      },
    });
    const activeTasksCount = await prisma.workReport.count({
      where: {
        user_id: { in: userIdArray },
        work_status: {
          in: ["RESUMED", "PAUSED"],
        },
      },
    });
    return res.status(200).json({
      taskCompleted: completedTasksCount,
      activeTasks: activeTasksCount,
    });
  } catch (error) {
    return handleError(res, error);
  }
};
