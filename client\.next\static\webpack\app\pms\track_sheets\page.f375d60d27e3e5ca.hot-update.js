"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/track_sheets/page",{

/***/ "(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx":
/*!***************************************************!*\
  !*** ./app/pms/track_sheets/ClientSelectPage.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/_component/FancySelect */ \"(app-pages-browser)/./app/_component/FancySelect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/pms/track_sheets/TrackSheetContext.tsx\");\n/* harmony import */ var _ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ViewTrackSheet */ \"(app-pages-browser)/./app/pms/track_sheets/ViewTrackSheet.tsx\");\n/* harmony import */ var _ExportTrackSheet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ExportTrackSheet */ \"(app-pages-browser)/./app/pms/track_sheets/ExportTrackSheet.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ClientSelectPage = (param)=>{\n    let { permissions, client, carrierDataUpdate, clientDataUpdate } = param;\n    _s();\n    const [customFieldsMap, setCustomFieldsMap] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedClients, setSelectedClients] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trackSheetData, setTrackSheetData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        data: [],\n        datalength: 0\n    });\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const pageSize = parseInt(searchParams.get(\"pageSize\")) || 50;\n    const totalPages = Math.ceil(((trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.datalength) || 0) / pageSize);\n    const params = new URLSearchParams(searchParams);\n    const clientOptions = (client === null || client === void 0 ? void 0 : client.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.client_name\n        };\n    })) || [];\n    const mapCustomFields = (data)=>data.map((row)=>({\n                ...row,\n                customFields: (row.TrackSheetCustomFieldMapping || []).reduce((acc, mapping)=>{\n                    acc[mapping.customFieldId] = mapping.value;\n                    return acc;\n                }, {})\n            }));\n    const handleClientChange = (newSelectedClients)=>{\n        setSelectedClients(newSelectedClients);\n    };\n    // const  {customFieldsReloadTrigger} = useContext()\n    const { customFieldsReloadTrigger, deleteData } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_6__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchData = async ()=>{\n            if (selectedClients.length > 0) {\n                try {\n                    var _selectedClients_, _selectedClients_1;\n                    const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat((_selectedClients_ = selectedClients[0]) === null || _selectedClients_ === void 0 ? void 0 : _selectedClients_.value));\n                    const fieldsMap = {};\n                    (response.custom_fields || []).forEach((field)=>{\n                        fieldsMap[field.id] = {\n                            name: field.name,\n                            type: field.type\n                        };\n                    });\n                    setCustomFieldsMap(fieldsMap);\n                    if (!params.get(\"page\")) params.set(\"page\", \"1\");\n                    if (!params.get(\"pageSize\")) params.set(\"pageSize\", \"50\");\n                    const url = \"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_3__.trackSheets_routes.GETALL_TRACK_SHEETS, \"/\").concat((_selectedClients_1 = selectedClients[0]) === null || _selectedClients_1 === void 0 ? void 0 : _selectedClients_1.value, \"?\").concat(params.toString());\n                    const trackSheetResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAllData)(url);\n                    setTrackSheetData({\n                        ...trackSheetResponse,\n                        data: mapCustomFields(trackSheetResponse.data || [])\n                    });\n                } catch (error) {\n                    setCustomFieldsMap({});\n                    setTrackSheetData({\n                        data: [],\n                        datalength: 0\n                    });\n                }\n            } else {\n                setCustomFieldsMap({});\n                setTrackSheetData({\n                    data: [],\n                    datalength: 0\n                });\n            }\n        };\n        fetchData();\n    }, [\n        selectedClients,\n        searchParams,\n        customFieldsReloadTrigger,\n        deleteData\n    ]);\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FancySelect__WEBPACK_IMPORTED_MODULE_1__.FancySelect, {\n                            frameworks: clientOptions,\n                            selected: selectedClients,\n                            setSelected: handleClientChange,\n                            label: \"Select Client\",\n                            placeholder: \"Search clients...\",\n                            className: \"max-w-xs\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExportTrackSheet__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        filteredTrackSheetData: trackSheetData === null || trackSheetData === void 0 ? void 0 : trackSheetData.data,\n                        customFieldsMap: customFieldsMap,\n                        columnVisibility: columnVisibility\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ViewTrackSheet__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    permissions: permissions,\n                    totalPages: totalPages,\n                    customFieldsMap: customFieldsMap,\n                    selectedClients: selectedClients,\n                    trackSheetData: trackSheetData,\n                    pageSize: pageSize,\n                    carrierDataUpdate: carrierDataUpdate,\n                    clientDataUpdate: clientDataUpdate,\n                    setColumnVisibility: setColumnVisibility\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\track_sheets\\\\ClientSelectPage.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientSelectPage, \"xiXTvZ12mn8MxUttMYHceyyxm+c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams\n    ];\n});\n_c = ClientSelectPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ClientSelectPage);\nvar _c;\n$RefreshReg$(_c, \"ClientSelectPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/track_sheets/ClientSelectPage.tsx\n"));

/***/ })

});