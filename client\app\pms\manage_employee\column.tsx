import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import UpdateEmployee from "./UpdateEmployee";
import DeleteRow from "@/app/_component/DeleteRow";
import { employee_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { ColDef } from "ag-grid-community";
import PinnedHeader from "@/app/_component/PinnedHeader";

export interface Employee {
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  user_type: string;
  id: string;
  role: any;
  level: number;
  parent_id: number | null;
}

export const column = (
  permissions: string[],
  allRoles: any,
  usertitle: any,
  allEmployee: any,
  allUsers: any,
  allClient: any,
  allBranch: any
): ColDef<any>[] => [
  // {
  //   field: "Sr. No.",
  //   headerName: "Sr. No.",
  //   cell: ({ row }) => row.index + 1,
  // },
  {
    field: "username",
    headerName: "Username",
    valueGetter: (params) => params?.data?.username,
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
  },
  {
    field: "firstName",
    headerName: "First Name",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
  },
  {
    field: "lastName",
    headerName: "Last Name",
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
  },
  {
    field: "email",
    headerName: "Email",
    filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },

      headerComponent: PinnedHeader,
  },
  // {
  //   field: "user_type",
  //   headerName: "User Type",
  // },
  {
    field: "Role",
    headerName: "Role",
    // accessorFn: (row) => row?.role?.name,
    valueGetter: (params) => params?.data?.role?.name,
    filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["apply", "reset"],
      },

      headerComponent: PinnedHeader,
  },
  {
    field: "Manager",
    headerName: "Reporting To",
    valueGetter: (params: any) => {
      const parentId = params?.data?.parent_id;

      const parent = allUsers?.find((user) => user.id === parentId);

      return parent ? parent.username : "-";
    },
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
    // valueGetter: (params: any) => {
    //   const managerName = params?.data?.parent_id;

    //   return <>{managerName}</>;
    // },
    // filter: "agTextColumnFilter",
    // filterParams: {
    //   buttons: ["clear"],
    // },
  },

  // accessorFn: (row) => console.log(row),
  // cell: ({ row }) => {
  //   const parentId = row?.original?.parent_id  ;
  //   const parent = allEmployee.find((user: any) => user.id === parentId);

  // const parentName = parent ? `${parent.username} ` : '-';
  //   return <>{parentName}</>;
  // },
  // },
  {
    field: "Title",
    headerName: "Title",
    valueGetter: (params: any) => {
      const levelId = params?.data?.level;

      const level = usertitle?.find((user: any) => user.id === levelId);

      return level ? `${level.title} ` : "-";
    },
    filter: "agTextColumnFilter",
    filterParams: {
      buttons: ["apply", "reset"],
    },

    headerComponent: PinnedHeader,
    // cell: ({ row }) => {
    //   const titleName = row?.getValue("Title");
    //   return <>{titleName}</>;
    // },
    //     cell: ({ row }) => {
    //       const levelId = row?.original?.level;
    //     const level = usertitle.find((user: any) => user.id === levelId);
    // console.log(level)
    //     const levelName = level ? `${level.title} ` : '-';

    //     return <>{levelName}</>;
    //     },
  },
  {
    field: "action",
    headerName: "Action",
    // id: "action",
    sortable: false,
    minWidth: 100,
    cellStyle: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      textAlign: "center", // Fallback
    },
    cellRenderer: (params: any) => {
      const employee = params?.data;

      return (
        <div className="flex items-center">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["update-user"]}
          >
            <UpdateEmployee
              data={employee}
              allRoles={allRoles}
              usertitle={usertitle}
              allUsers={allUsers}
              allClient={allClient} 
              allBranch={allBranch}
            />
          </PermissionWrapper>
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-user"]}
          >
            <DeleteRow
              route={`${employee_routes.DELETE_USERS}/${employee?.id}`}
            />
          </PermissionWrapper>
        </div>
      );
    },
  },
];
